import { Component, h, Prop, State, Element } from '@stencil/core';
import { getResponsesApi, ResponseFilters } from './helpers';

@Component({
  tag: 'p-survey-responses',
  styleUrl: 'p-survey-responses.css',
  shadow: true,
})
export class PSurveyResponses {
  @Element() el: HTMLElement;
  @Prop() surveyId: string;
  @Prop() survey: any;

  @State() selectedTimeFilter: string = 'all-time';

  @State() responses: any[] = [];
  @State() analytics: any = {};
  @State() loading: boolean = false;
  @State() error: string = null;
  @State() selectedResponse: any = null;
  @State() showModal: boolean = false;

  private timeFilterOptions = [
    { label: 'All time', value: 'all-time' },
    { label: 'Last 7 days', value: '7-days' },
    { label: 'Last 30 days', value: '30-days' },
    { label: 'Last 90 days', value: '90-days' },
  ];

  async componentDidLoad() {
    await this.fetchResponses();
  }

  private async fetchResponses() {
    if (!this.surveyId) return;

    this.loading = true;
    this.error = null;

    try {
      const filters: ResponseFilters = {
        timeFilter: this.selectedTimeFilter,
      };

      const result = await getResponsesApi(this.surveyId, filters);

      if (result.success) {
        this.responses = result.payload.responses || [];
        this.analytics = result.payload || {};
        await this.renderGraph();
      } else {
        this.error = result.message;
      }
    } catch (error) {
      this.error = 'Failed to fetch responses';
      console.error('Error fetching responses:', error);
    } finally {
      this.loading = false;
    }
  }

  private async renderGraph() {
    try {
      // Dynamically import Plotly to avoid bundle size issues
      const Plotly = await import('plotly.js-dist-min');

      const graphElement = this.el.shadowRoot.querySelector('#responses-graph');
      if (!graphElement || !this.analytics.responsesByDay) return;

      // Prepare data for the bar chart
      const dates = Object.keys(this.analytics.responsesByDay).sort();
      const counts = dates.map(date => this.analytics.responsesByDay[date]);

      const data = [
        {
          x: dates,
          y: counts,
          type: 'bar',
          marker: {
            color: '#3A8DFF',
          },
          name: 'Responses',
          hovertemplate: '<b>%{y} responses</b><br>Date: %{x}<extra></extra>',
        },
      ];

      const layout = {
        xaxis: {
          title: 'Date',
          showgrid: false,
          gridcolor: '#f0f0f0',
          // Remove rangemode for x-axis to avoid unnecessary zero
          showticklabels: dates.length > 0, // Hide x-axis labels when no data
        },
        yaxis: {
          title: 'Number of Responses',
          showgrid: true,
          gridcolor: '#f0f0f0',
          zerolinecolor: '#f0f0f0', // Make zero line same color as gridlines
          tickformat: 'd', // Format as integers (no decimals)
          dtick: 1, // Force tick intervals of 1 to avoid fractional ticks
          rangemode: 'tozero',
          showticklabels: dates.length > 0, // Hide y-axis labels when no data
        },
        plot_bgcolor: 'white',
        paper_bgcolor: 'white',
        margin: { t: 0, r: 0, b: 20, l: 20 }, // Reduced margins to maximize chart area
        height: 320,
        width: undefined, // Let it auto-size to container
        autosize: true,
      };

      const config = {
        displayModeBar: false,
        responsive: true,
        staticPlot: false,
        scrollZoom: false,
        doubleClick: false,
        displaylogo: false,
        modeBarButtonsToRemove: [
          'pan2d',
          'select2d',
          'lasso2d',
          'resetScale2d',
          'zoomIn2d',
          'zoomOut2d',
        ],
      };

      await Plotly.newPlot(graphElement, data, layout, config);

      // Ensure proper containment after plot is created
      setTimeout(() => {
        const plotlyDiv = graphElement.querySelector('.plotly');
        if (plotlyDiv) {
          (plotlyDiv as HTMLElement).style.position = 'relative';
          (plotlyDiv as HTMLElement).style.zIndex = '1';
          (plotlyDiv as HTMLElement).style.overflow = 'hidden';
        }

        const svgContainer = graphElement.querySelector('.svg-container');
        if (svgContainer) {
          (svgContainer as HTMLElement).style.position = 'relative';
          (svgContainer as HTMLElement).style.zIndex = '1';
          (svgContainer as HTMLElement).style.overflow = 'hidden';
        }

        const svg = graphElement.querySelector('svg');
        if (svg) {
          svg.style.position = 'relative';
          svg.style.zIndex = '1';
          svg.style.maxWidth = '100%';
          svg.style.maxHeight = '100%';
        }
      }, 100);

      // Add click event handler to graph points
      graphElement.addEventListener('plotly_click', (eventData: any) => {
        if (eventData.points && eventData.points.length > 0) {
          const clickedDate = eventData.points[0].x;
          // Find responses for the clicked date
          const responsesForDate = this.responses.filter(response => {
            const responseDate = new Date(response.created_at).toISOString().split('T')[0];
            return responseDate === clickedDate;
          });

          // If there are responses for this date, open modal for the first one
          // In a more sophisticated implementation, you might show a list to choose from
          if (responsesForDate.length > 0) {
            this.openResponseModal(responsesForDate[0]);
          }
        }
      });
    } catch (error) {
      console.error('Error rendering graph:', error);
    }
  }

  private handleTimeFilterChange = async (event: any) => {
    this.selectedTimeFilter = event.detail.value;
    await this.fetchResponses();
  };

  private getRespondentName(response: any): string {
    return response.respondent_details?.name || response.respondent_details?.firstName || '-';
  }

  private getRespondentEmail(response: any): string {
    return response.respondent_details?.email || '-';
  }

  private formatDate(dateString: string): string {
    const date = new Date(dateString);
    // Format to local time without milliseconds
    return date.toLocaleString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  }

  private formatObjectForDisplay(obj: any): any {
    if (!obj || typeof obj !== 'object') {
      return obj;
    }

    const formatKey = (key: string): string => {
      // Convert camelCase to space-separated words
      const spaced = key.replace(/([A-Z])/g, ' $1');
      // Convert to uppercase
      return spaced.toUpperCase().trim();
    };

    const formatValue = (value: any, parentKey: string): any => {
      // Handle empty, null, undefined, or empty string values
      if (
        value === null ||
        value === undefined ||
        value === '' ||
        (typeof value === 'string' && value.trim() === '')
      ) {
        return '-';
      }

      // Handle nested objects (like userAgent) - return as array for list rendering
      if (typeof value === 'object' && value !== null) {
        return Object.entries(value)
          .filter(([nestedKey, _]) => {
            // Filter out specific fields based on parent key
            if (parentKey.toLowerCase() === 'useragent') {
              return nestedKey !== 'onLine' && nestedKey !== 'timestamp';
            }
            return true;
          })
          .map(([nestedKey, nestedValue]) => ({
            key: nestedKey,
            value: String(nestedValue), // Convert all values to strings to ensure visibility
          }));
      }

      return value;
    };

    return Object.entries(obj)
      .filter(([key, _]) => {
        // Filter out IP address from metadata
        return key.toLowerCase() !== 'ipaddress';
      })
      .map(([key, value]) => ({
        label: formatKey(key),
        value: formatValue(value, key),
      }));
  }

  private renderValue(value: any) {
    // If value is an array of key-value pairs (nested object), render as list
    if (
      Array.isArray(value) &&
      value.length > 0 &&
      typeof value[0] === 'object' &&
      'key' in value[0]
    ) {
      return (
        <ul style={{ margin: '0', paddingLeft: '1.5em' }}>
          {value.map((item: any) => (
            <li key={item.key}>
              <e-text>
                {item.key}: {item.value}
              </e-text>
            </li>
          ))}
        </ul>
      );
    }

    // For simple values, render as text
    return <e-text>{value}</e-text>;
  }

  private getCompletionRate(): number {
    if (!this.responses.length) return 0;

    const completedResponses = this.responses.filter(response => !response.is_discarded).length;
    const totalResponses = this.responses.length;

    return Math.round((completedResponses / totalResponses) * 100);
  }

  private openResponseModal = (response: any) => {
    this.selectedResponse = response;
    this.showModal = true;
  };

  private closeModal = () => {
    this.showModal = false;
    this.selectedResponse = null;
  };

  private deleteResponse = async (responseId: string) => {
    if (confirm('Are you sure you want to delete this response? This action cannot be undone.')) {
      try {
        // TODO: Implement delete API call
        console.log('Deleting response:', responseId);
        // After successful deletion, reload the responses
        await this.fetchResponses();
      } catch (error) {
        console.error('Error deleting response:', error);
        alert('Failed to delete response. Please try again.');
      }
    }
  };

  private renderSkeletonCards() {
    return (
      <l-row>
        <c-card style={{ flex: '1', marginRight: '1em' }}>
          <div style={{ textAlign: 'center' }}>
            <e-text variant="footnote">TOTAL RESPONSES</e-text>
            <l-spacer value={0.25}></l-spacer>
            <div
              style={{
                height: '2.5em',
                backgroundColor: 'var(--color__grey--100)',
                borderRadius: '4px',
                animation: 'pulse 1.5s ease-in-out infinite',
              }}
            ></div>
          </div>
        </c-card>
        <c-card style={{ flex: '1', marginRight: '1em' }}>
          <div style={{ textAlign: 'center' }}>
            <e-text variant="footnote">COMPLETION RATE</e-text>
            <l-spacer value={0.25}></l-spacer>
            <div
              style={{
                height: '2.5em',
                backgroundColor: 'var(--color__grey--100)',
                borderRadius: '4px',
                animation: 'pulse 1.5s ease-in-out infinite',
              }}
            ></div>
          </div>
        </c-card>
        <c-card style={{ flex: '1' }}>
          <div style={{ textAlign: 'center' }}>
            <e-text variant="footnote">AVERAGE TIME</e-text>
            <l-spacer value={0.25}></l-spacer>
            <div
              style={{
                height: '2.5em',
                backgroundColor: 'var(--color__grey--100)',
                borderRadius: '4px',
                animation: 'pulse 1.5s ease-in-out infinite',
              }}
            ></div>
          </div>
        </c-card>
      </l-row>
    );
  }

  private renderSkeletonGraph() {
    return (
      <c-card style={{ padding: '0' }}>
        <div
          style={{
            width: '100%',
            height: '320px',
            backgroundColor: 'var(--color__grey--100)',
            borderRadius: '4px',
            animation: 'pulse 1.5s ease-in-out infinite',
          }}
        ></div>
      </c-card>
    );
  }

  render() {
    return (
      <div>
        {/* Header with title and filters */}
        <l-row justifyContent="space-between" align="center">
          <e-text variant="heading">Responses</e-text>

          <l-row align="center">
            <e-select
              value={this.selectedTimeFilter}
              options={JSON.stringify(this.timeFilterOptions)}
              name="timeFilter"
              onSelectChangeEvent={this.handleTimeFilterChange}
              style={{ marginRight: '1em' }}
            />
          </l-row>
        </l-row>

        <l-spacer value={1}></l-spacer>

        {/* Error State */}
        {this.error && (
          <c-card>
            <div style={{ textAlign: 'center', padding: '2em' }}>
              <e-text>
                <strong>{this.error}</strong>
              </e-text>
            </div>
          </c-card>
        )}

        {/* Overview Cards - Show skeleton while loading, real data when loaded */}
        {this.loading ? (
          this.renderSkeletonCards()
        ) : (
          <l-row>
            <c-card style={{ flex: '1', marginRight: '1em' }}>
              <div style={{ textAlign: 'center' }}>
                <e-text variant="footnote">TOTAL RESPONSES</e-text>
                <l-spacer value={0.25}></l-spacer>
                <e-text variant="display">{this.analytics.totalResponses || 0}</e-text>
              </div>
            </c-card>
            <c-card style={{ flex: '1', marginRight: '1em' }}>
              <div style={{ textAlign: 'center' }}>
                <e-text variant="footnote">COMPLETION RATE</e-text>
                <l-spacer value={0.25}></l-spacer>
                <e-text variant="display">{this.getCompletionRate()}%</e-text>
              </div>
            </c-card>
            <c-card style={{ flex: '1' }}>
              <div style={{ textAlign: 'center' }}>
                <e-text variant="footnote">AVERAGE TIME</e-text>
                <l-spacer value={0.25}></l-spacer>
                <e-text variant="display">{this.analytics.avgCompletionTime || 0}m</e-text>
              </div>
            </c-card>
          </l-row>
        )}

        <l-spacer value={2}></l-spacer>

        {/* Graph - Show skeleton while loading, real graph when loaded */}
        {this.loading
          ? this.renderSkeletonGraph()
          : this.analytics.responsesByDay && (
              <c-card style={{ padding: '0' }}>
                <div
                  id="responses-graph"
                  style={{
                    width: '100%',
                    height: '320px',
                    position: 'relative',
                    overflow: 'hidden',
                    zIndex: '1',
                  }}
                ></div>
              </c-card>
            )}

        <l-spacer value={2}></l-spacer>

        {/* Responses Content */}
        <c-card>
          <div class="response-table">
            <div class="table-header">
              <div class="table-cell">
                <e-text variant="footnote">NAME</e-text>
              </div>
              <div class="table-cell">
                <e-text variant="footnote">EMAIL</e-text>
              </div>
              <div class="table-cell">
                <e-text variant="footnote">SUBMITTED ON</e-text>
              </div>
            </div>
            <l-separator></l-separator>

            {this.loading ? (
              // Show skeleton rows while loading
              <div>
                {[1, 2, 3].map(index => (
                  <div key={index} class="table-row">
                    <div class="table-cell">
                      <div
                        style={{
                          height: '1.2em',
                          backgroundColor: 'var(--color__grey--100)',
                          borderRadius: '4px',
                          animation: 'pulse 1.5s ease-in-out infinite',
                        }}
                      ></div>
                    </div>
                    <div class="table-cell">
                      <div
                        style={{
                          height: '1.2em',
                          backgroundColor: 'var(--color__grey--100)',
                          borderRadius: '4px',
                          animation: 'pulse 1.5s ease-in-out infinite',
                        }}
                      ></div>
                    </div>
                    <div class="table-cell">
                      <div
                        style={{
                          height: '1.2em',
                          backgroundColor: 'var(--color__grey--100)',
                          borderRadius: '4px',
                          animation: 'pulse 1.5s ease-in-out infinite',
                        }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : this.responses.length === 0 ? (
              // Show empty state within table structure
              <div
                class="table-row table-row--no-response"
                style={{ textAlign: 'center', padding: '2em 0' }}
              >
                <div style={{ width: '100%', textAlign: 'center' }}>
                  <e-text>
                    <strong>No Responses Yet</strong>
                    <br />
                    Data will appear here once participants start responding to the survey
                  </e-text>
                </div>
              </div>
            ) : (
              // Show actual response data
              this.responses.map(response => (
                <div
                  key={response.id}
                  class="table-row"
                  onClick={() => this.openResponseModal(response)}
                >
                  <div class="table-cell">{this.getRespondentName(response)}</div>
                  <div class="table-cell">{this.getRespondentEmail(response)}</div>
                  <div class="table-cell">{this.formatDate(response.created_at)}</div>
                  <div class="table-cell-action">
                    <button
                      class="delete-button"
                      onClick={e => {
                        e.stopPropagation();
                        this.deleteResponse(response.id);
                      }}
                      title="Delete response"
                    >
                      <e-image src="../../../assets/icon/red/trash-red.svg" width="1em"></e-image>
                    </button>
                  </div>
                </div>
              ))
            )}
          </div>
        </c-card>

        {/* Response Detail Modal */}
        <p-modal
          is-open={this.showModal}
          modal-title="Response Details"
          onModalCloseEvent={this.closeModal}
        >
          {this.showModal && this.selectedResponse && (
            <div>
              <e-text variant="footnote">
                Submitted On: {this.formatDate(this.selectedResponse.created_at)}
              </e-text>

              {/* Always show name and email */}
              <l-spacer value={1.5}></l-spacer>
              <c-card>
                <div class="response-section">
                  <e-text>
                    <strong>Respondent Information</strong>
                  </e-text>
                  <l-spacer value={1}></l-spacer>
                  <div style={{ marginBottom: '1em' }}>
                    <e-text variant="footnote">NAME</e-text>
                    <e-text>{this.getRespondentName(this.selectedResponse)}</e-text>
                  </div>
                  <div style={{ marginBottom: '1em' }}>
                    <e-text variant="footnote">EMAIL</e-text>
                    <e-text>{this.getRespondentEmail(this.selectedResponse)}</e-text>
                  </div>
                </div>
              </c-card>

              {this.selectedResponse.respondent_details && (
                <div>
                  <l-spacer value={1.5}></l-spacer>
                  <c-card>
                    <div class="response-section">
                      <e-text>
                        <strong>Respondent Details</strong>
                      </e-text>
                      <l-spacer value={1}></l-spacer>
                      {this.formatObjectForDisplay(this.selectedResponse.respondent_details).map(
                        (item: any) => (
                          <div style={{ marginBottom: '1em' }}>
                            <e-text variant="footnote">{item.label}</e-text>
                            {this.renderValue(item.value)}
                          </div>
                        ),
                      )}
                    </div>
                  </c-card>
                </div>
              )}

              <l-spacer value={1.5}></l-spacer>
              <c-card>
                <div class="response-section">
                  <e-text>
                    <strong>Response Data</strong>
                  </e-text>
                  <l-spacer value={1}></l-spacer>
                  {this.formatObjectForDisplay(this.selectedResponse.response_data).map(
                    (item: any) => (
                      <div style={{ marginBottom: '1em' }}>
                        <e-text variant="footnote">{item.label}</e-text>
                        {this.renderValue(item.value)}
                      </div>
                    ),
                  )}
                </div>
              </c-card>

              {this.selectedResponse.meta && (
                <div>
                  <l-spacer value={1.5}></l-spacer>
                  <c-card>
                    <div class="response-section">
                      <e-text>
                        <strong>Metadata</strong>
                      </e-text>
                      <l-spacer value={1}></l-spacer>
                      {this.formatObjectForDisplay(this.selectedResponse.meta).map((item: any) => (
                        <div style={{ marginBottom: '1em' }}>
                          <e-text variant="footnote">{item.label}</e-text>
                          {this.renderValue(item.value)}
                        </div>
                      ))}
                    </div>
                  </c-card>
                </div>
              )}
            </div>
          )}
        </p-modal>
      </div>
    );
  }
}
